#!/bin/bash

# Project Name
PROJECT_NAME=$1

if [ -z "$PROJECT_NAME" ]; then
  echo "❌ Please provide a project name: ./setup-laravel-inertia.sh project-name"
  exit 1
fi

echo "🚀 Creating Laravel project: $PROJECT_NAME"
composer create-project laravel/laravel "$PROJECT_NAME"

cd "$PROJECT_NAME" || exit

echo "✅ Installing Laravel Breeze"
composer require laravel/breeze --dev
php artisan breeze:install vue --inertia

echo "🔧 Installing NPM dependencies"
npm install && npm run dev

echo "🛠️ Running migrations"
php artisan migrate

echo "✅ Laravel with Inertia.js + Vue.js + Breeze is ready!"
echo "👉 Project path: $(pwd)"
