import { useState, useEffect } from 'react';

const OrderManagement = () => {
  const [orders, setOrders] = useState([]);
  const [menuItems, setMenuItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingOrder, setEditingOrder] = useState(null);
  const [stats, setStats] = useState({});

  const [formData, setFormData] = useState({
    customerName: '',
    customerPhone: '',
    customerEmail: '',
    orderType: 'dine-in',
    tableNumber: '',
    deliveryAddress: '',
    notes: '',
    items: []
  });

  const API_BASE = 'http://localhost:5000/api';

  useEffect(() => {
    fetchOrders();
    fetchMenuItems();
    fetchStats();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE}/orders`);
      const data = await response.json();
      setOrders(data.orders || []);
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchMenuItems = async () => {
    try {
      const response = await fetch(`${API_BASE}/menu`);
      const data = await response.json();
      setMenuItems(data);
    } catch (error) {
      console.error('Error fetching menu items:', error);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch(`${API_BASE}/orders/stats`);
      const data = await response.json();
      setStats(data);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const url = editingOrder
        ? `${API_BASE}/orders/${editingOrder._id}`
        : `${API_BASE}/orders`;

      const method = editingOrder ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        fetchOrders();
        fetchStats();
        resetForm();
      }
    } catch (error) {
      console.error('Error saving order:', error);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this order?')) {
      try {
        await fetch(`${API_BASE}/orders/${id}`, {
          method: 'DELETE',
        });
        fetchOrders();
        fetchStats();
      } catch (error) {
        console.error('Error deleting order:', error);
      }
    }
  };

  const updateOrderStatus = async (id, status) => {
    try {
      await fetch(`${API_BASE}/orders/${id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });
      fetchOrders();
      fetchStats();
    } catch (error) {
      console.error('Error updating order status:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      customerName: '',
      customerPhone: '',
      customerEmail: '',
      orderType: 'dine-in',
      tableNumber: '',
      deliveryAddress: '',
      notes: '',
      items: []
    });
    setShowCreateForm(false);
    setEditingOrder(null);
  };

  const addItemToOrder = () => {
    setFormData({
      ...formData,
      items: [...formData.items, { menuItem: '', quantity: 1 }]
    });
  };

  const removeItemFromOrder = (index) => {
    const newItems = formData.items.filter((_, i) => i !== index);
    setFormData({ ...formData, items: newItems });
  };

  const updateOrderItem = (index, field, value) => {
    const newItems = [...formData.items];
    newItems[index][field] = value;
    setFormData({ ...formData, items: newItems });
  };

  const startEdit = (order) => {
    setEditingOrder(order);
    setFormData({
      customerName: order.customerName,
      customerPhone: order.customerPhone,
      customerEmail: order.customerEmail || '',
      orderType: order.orderType,
      tableNumber: order.tableNumber || '',
      deliveryAddress: order.deliveryAddress || '',
      notes: order.notes || '',
      items: order.items.map(item => ({
        menuItem: item.menuItem._id,
        quantity: item.quantity
      }))
    });
    setShowCreateForm(true);
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: 'bg-yellow-500',
      confirmed: 'bg-blue-500',
      preparing: 'bg-orange-500',
      ready: 'bg-green-500',
      delivered: 'bg-gray-500',
      cancelled: 'bg-red-500'
    };
    return colors[status] || 'bg-gray-500';
  };

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="backdrop-blur-lg bg-white/10 rounded-xl p-4 border border-white/20">
          <h3 className="text-white/80 text-sm">Today's Orders</h3>
          <p className="text-2xl font-bold text-white">{stats.todayOrders || 0}</p>
        </div>
        <div className="backdrop-blur-lg bg-white/10 rounded-xl p-4 border border-white/20">
          <h3 className="text-white/80 text-sm">Total Revenue</h3>
          <p className="text-2xl font-bold text-white">${stats.totalRevenue || 0}</p>
        </div>
        <div className="backdrop-blur-lg bg-white/10 rounded-xl p-4 border border-white/20">
          <h3 className="text-white/80 text-sm">Total Orders</h3>
          <p className="text-2xl font-bold text-white">{orders.length}</p>
        </div>
        <div className="backdrop-blur-lg bg-white/10 rounded-xl p-4 border border-white/20">
          <button
            onClick={() => setShowCreateForm(true)}
            className="w-full bg-white/20 hover:bg-white/30 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
          >
            + New Order
          </button>
        </div>
      </div>

      {/* Create/Edit Form */}
      {showCreateForm && (
        <div className="backdrop-blur-lg bg-white/10 rounded-xl p-6 border border-white/20">
          <h2 className="text-xl font-bold text-white mb-4">
            {editingOrder ? 'Edit Order' : 'Create New Order'}
          </h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <input
                type="text"
                placeholder="Customer Name"
                value={formData.customerName}
                onChange={(e) => setFormData({ ...formData, customerName: e.target.value })}
                className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/60"
                required
              />
              <input
                type="tel"
                placeholder="Customer Phone"
                value={formData.customerPhone}
                onChange={(e) => setFormData({ ...formData, customerPhone: e.target.value })}
                className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/60"
                required
              />
              <input
                type="email"
                placeholder="Customer Email (Optional)"
                value={formData.customerEmail}
                onChange={(e) => setFormData({ ...formData, customerEmail: e.target.value })}
                className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/60"
              />
              <select
                value={formData.orderType}
                onChange={(e) => setFormData({ ...formData, orderType: e.target.value })}
                className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white"
              >
                <option value="dine-in">Dine In</option>
                <option value="takeaway">Takeaway</option>
                <option value="delivery">Delivery</option>
              </select>
            </div>

            {formData.orderType === 'dine-in' && (
              <input
                type="number"
                placeholder="Table Number"
                value={formData.tableNumber}
                onChange={(e) => setFormData({ ...formData, tableNumber: e.target.value })}
                className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/60"
                required
              />
            )}

            {formData.orderType === 'delivery' && (
              <textarea
                placeholder="Delivery Address"
                value={formData.deliveryAddress}
                onChange={(e) => setFormData({ ...formData, deliveryAddress: e.target.value })}
                className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/60 w-full"
                required
              />
            )}

            <textarea
              placeholder="Notes (Optional)"
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/60 w-full"
            />

            {/* Order Items */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-white font-semibold">Order Items</h3>
                <button
                  type="button"
                  onClick={addItemToOrder}
                  className="bg-white/20 hover:bg-white/30 text-white px-3 py-1 rounded text-sm"
                >
                  Add Item
                </button>
              </div>
              {formData.items.map((item, index) => (
                <div key={index} className="flex gap-2 mb-2">
                  <select
                    value={item.menuItem}
                    onChange={(e) => updateOrderItem(index, 'menuItem', e.target.value)}
                    className="flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white"
                    required
                  >
                    <option value="">Select Menu Item</option>
                    {menuItems.map((menuItem) => (
                      <option key={menuItem._id} value={menuItem._id}>
                        {menuItem.name} - ${menuItem.price}
                      </option>
                    ))}
                  </select>
                  <input
                    type="number"
                    min="1"
                    value={item.quantity}
                    onChange={(e) => updateOrderItem(index, 'quantity', parseInt(e.target.value))}
                    className="w-20 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => removeItemFromOrder(index)}
                    className="bg-red-500/20 hover:bg-red-500/30 text-red-300 px-3 py-2 rounded"
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>

            <div className="flex gap-2">
              <button
                type="submit"
                className="bg-green-500/20 hover:bg-green-500/30 text-green-300 font-semibold py-2 px-4 rounded-lg"
              >
                {editingOrder ? 'Update Order' : 'Create Order'}
              </button>
              <button
                type="button"
                onClick={resetForm}
                className="bg-gray-500/20 hover:bg-gray-500/30 text-gray-300 font-semibold py-2 px-4 rounded-lg"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Orders List */}
      <div className="backdrop-blur-lg bg-white/10 rounded-xl p-6 border border-white/20">
        <h2 className="text-xl font-bold text-white mb-4">Orders</h2>
        {loading ? (
          <p className="text-white/60">Loading orders...</p>
        ) : orders.length === 0 ? (
          <p className="text-white/60">No orders found.</p>
        ) : (
          <div className="space-y-4">
            {orders.map((order) => (
              <div key={order._id} className="bg-white/5 rounded-lg p-4 border border-white/10">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="text-white font-semibold">#{order.orderNumber}</h3>
                    <p className="text-white/80">{order.customerName} - {order.customerPhone}</p>
                    <p className="text-white/60 text-sm">{order.orderType.toUpperCase()}</p>
                  </div>
                  <div className="text-right">
                    <span className={`inline-block px-2 py-1 rounded text-xs text-white ${getStatusColor(order.status)}`}>
                      {order.status.toUpperCase()}
                    </span>
                    <p className="text-white font-bold mt-1">${order.totalAmount}</p>
                  </div>
                </div>

                <div className="mb-3">
                  <h4 className="text-white/80 text-sm mb-1">Items:</h4>
                  {order.items.map((item, index) => (
                    <p key={index} className="text-white/60 text-sm">
                      {item.quantity}x {item.name} - ${item.price * item.quantity}
                    </p>
                  ))}
                </div>

                <div className="flex gap-2 flex-wrap">
                  <select
                    value={order.status}
                    onChange={(e) => updateOrderStatus(order._id, e.target.value)}
                    className="bg-white/10 border border-white/20 rounded px-2 py-1 text-white text-sm"
                  >
                    <option value="pending">Pending</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="preparing">Preparing</option>
                    <option value="ready">Ready</option>
                    <option value="delivered">Delivered</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                  <button
                    onClick={() => startEdit(order)}
                    className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 px-3 py-1 rounded text-sm"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(order._id)}
                    className="bg-red-500/20 hover:bg-red-500/30 text-red-300 px-3 py-1 rounded text-sm"
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderManagement;
