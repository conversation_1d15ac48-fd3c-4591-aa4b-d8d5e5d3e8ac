const express = require('express');
const router = express.Router();
const {
  getOrders,
  createOrder,
  getOrderById,
  updateOrder,
  deleteOrder,
  updateOrderStatus,
  getOrderStats
} = require('../controllers/orderController');

// GET /api/orders/stats - Get order statistics
router.get('/stats', getOrderStats);

// GET /api/orders - Get all orders (with filtering and pagination)
router.get('/', getOrders);

// POST /api/orders - Create new order
router.post('/', createOrder);

// GET /api/orders/:id - Get order by ID
router.get('/:id', getOrderById);

// PUT /api/orders/:id - Update order
router.put('/:id', updateOrder);

// PATCH /api/orders/:id/status - Update order status only
router.patch('/:id/status', updateOrderStatus);

// DELETE /api/orders/:id - Delete order
router.delete('/:id', deleteOrder);

module.exports = router;
