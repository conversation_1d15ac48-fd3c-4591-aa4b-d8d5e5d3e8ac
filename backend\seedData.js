const mongoose = require('mongoose');
const MenuItem = require('./models/MenuItem');
require('dotenv').config();

const sampleMenuItems = [
  {
    name: "Margherita Pizza",
    price: 12.99,
    category: "main"
  },
  {
    name: "Caesar Salad",
    price: 8.99,
    category: "appetizer"
  },
  {
    name: "Grilled Chicken",
    price: 15.99,
    category: "main"
  },
  {
    name: "Chocolate Cake",
    price: 6.99,
    category: "dessert"
  },
  {
    name: "Coca Cola",
    price: 2.99,
    category: "beverage"
  },
  {
    name: "Garlic Bread",
    price: 4.99,
    category: "appetizer"
  },
  {
    name: "Beef Burger",
    price: 13.99,
    category: "main"
  },
  {
    name: "Ice Cream",
    price: 4.99,
    category: "dessert"
  }
];

async function seedDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing menu items
    await MenuItem.deleteMany({});
    console.log('Cleared existing menu items');

    // Insert sample menu items
    await MenuItem.insertMany(sampleMenuItems);
    console.log('Sample menu items inserted successfully');

    mongoose.connection.close();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
}

seedDatabase();
