const Order = require('../models/Order');
const MenuItem = require('../models/MenuItem');

// Get all orders
const getOrders = async (req, res) => {
  try {
    const { status, orderType, page = 1, limit = 10 } = req.query;
    const filter = {};
    
    if (status) filter.status = status;
    if (orderType) filter.orderType = orderType;

    const orders = await Order.find(filter)
      .populate('items.menuItem')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Order.countDocuments(filter);

    res.json({
      orders,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Create new order
const createOrder = async (req, res) => {
  try {
    const { customerName, customerPhone, customerEmail, items, orderType, tableNumber, deliveryAddress, notes } = req.body;

    // Validate and calculate total
    let totalAmount = 0;
    const orderItems = [];

    for (const item of items) {
      const menuItem = await MenuItem.findById(item.menuItem);
      if (!menuItem) {
        return res.status(400).json({ message: `Menu item with ID ${item.menuItem} not found` });
      }

      const orderItem = {
        menuItem: menuItem._id,
        name: menuItem.name,
        price: menuItem.price,
        quantity: item.quantity
      };

      orderItems.push(orderItem);
      totalAmount += menuItem.price * item.quantity;
    }

    const order = new Order({
      customerName,
      customerPhone,
      customerEmail,
      items: orderItems,
      totalAmount,
      orderType,
      tableNumber,
      deliveryAddress,
      notes
    });

    const savedOrder = await order.save();
    await savedOrder.populate('items.menuItem');
    
    res.status(201).json(savedOrder);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Get order by ID
const getOrderById = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id).populate('items.menuItem');
    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }
    res.json(order);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Update order
const updateOrder = async (req, res) => {
  try {
    const { status, items, customerName, customerPhone, customerEmail, orderType, tableNumber, deliveryAddress, notes } = req.body;

    const order = await Order.findById(req.params.id);
    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }

    // Update basic fields
    if (customerName) order.customerName = customerName;
    if (customerPhone) order.customerPhone = customerPhone;
    if (customerEmail) order.customerEmail = customerEmail;
    if (orderType) order.orderType = orderType;
    if (tableNumber) order.tableNumber = tableNumber;
    if (deliveryAddress) order.deliveryAddress = deliveryAddress;
    if (notes) order.notes = notes;
    if (status) order.status = status;

    // Update items if provided
    if (items) {
      let totalAmount = 0;
      const orderItems = [];

      for (const item of items) {
        const menuItem = await MenuItem.findById(item.menuItem);
        if (!menuItem) {
          return res.status(400).json({ message: `Menu item with ID ${item.menuItem} not found` });
        }

        const orderItem = {
          menuItem: menuItem._id,
          name: menuItem.name,
          price: menuItem.price,
          quantity: item.quantity
        };

        orderItems.push(orderItem);
        totalAmount += menuItem.price * item.quantity;
      }

      order.items = orderItems;
      order.totalAmount = totalAmount;
    }

    const updatedOrder = await order.save();
    await updatedOrder.populate('items.menuItem');
    
    res.json(updatedOrder);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Delete order
const deleteOrder = async (req, res) => {
  try {
    const order = await Order.findByIdAndDelete(req.params.id);
    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }
    res.json({ message: 'Order deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Update order status
const updateOrderStatus = async (req, res) => {
  try {
    const { status } = req.body;
    const order = await Order.findByIdAndUpdate(
      req.params.id,
      { status },
      { new: true, runValidators: true }
    ).populate('items.menuItem');

    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }

    res.json(order);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Get order statistics
const getOrderStats = async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const stats = await Order.aggregate([
      {
        $facet: {
          todayOrders: [
            { $match: { createdAt: { $gte: today } } },
            { $count: "count" }
          ],
          totalRevenue: [
            { $match: { status: { $ne: 'cancelled' } } },
            { $group: { _id: null, total: { $sum: "$totalAmount" } } }
          ],
          statusCounts: [
            { $group: { _id: "$status", count: { $sum: 1 } } }
          ],
          orderTypeCounts: [
            { $group: { _id: "$orderType", count: { $sum: 1 } } }
          ]
        }
      }
    ]);

    const result = {
      todayOrders: stats[0].todayOrders[0]?.count || 0,
      totalRevenue: stats[0].totalRevenue[0]?.total || 0,
      statusCounts: stats[0].statusCounts,
      orderTypeCounts: stats[0].orderTypeCounts
    };

    res.json(result);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

module.exports = {
  getOrders,
  createOrder,
  getOrderById,
  updateOrder,
  deleteOrder,
  updateOrderStatus,
  getOrderStats
};
