import { useState } from 'react'
import './App.css'
import OrderManagement from './components/OrderManagement'

function App() {
  const [activeTab, setActiveTab] = useState('dashboard')

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-4xl font-bold text-white text-center mb-8">
          Restaurant Dashboard
        </h1>

        {/* Navigation */}
        <div className="flex justify-center mb-8">
          <div className="backdrop-blur-lg bg-white/10 rounded-xl p-2 border border-white/20">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={`px-6 py-2 rounded-lg font-semibold transition-colors ${
                activeTab === 'dashboard'
                  ? 'bg-white/20 text-white'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              Dashboard
            </button>
            <button
              onClick={() => setActiveTab('orders')}
              className={`px-6 py-2 rounded-lg font-semibold transition-colors ${
                activeTab === 'orders'
                  ? 'bg-white/20 text-white'
                  : 'text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              Orders
            </button>
          </div>
        </div>

        {/* Content */}
        {activeTab === 'dashboard' && (
          <div className="flex items-center justify-center">
            <div className="backdrop-blur-lg bg-white/10 rounded-xl p-8 shadow-2xl border border-white/20 max-w-md w-full">
              <div className="space-y-4">
                <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                  <h2 className="text-xl font-semibold text-white mb-2">Welcome</h2>
                  <p className="text-white/80">
                    Your restaurant management system is ready to use.
                  </p>
                </div>
                <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                  <h3 className="text-lg font-medium text-white mb-2">Quick Actions</h3>
                  <button
                    onClick={() => setActiveTab('orders')}
                    className="w-full bg-white/20 hover:bg-white/30 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
                  >
                    Manage Orders
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'orders' && <OrderManagement />}
      </div>
    </div>
  )
}

export default App
